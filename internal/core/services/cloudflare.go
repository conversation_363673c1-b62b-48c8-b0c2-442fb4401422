package services

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"ops-api/internal/core/dto"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"sync"
	"time"
)

type CloudflareService struct {
	httpClient *http.Client
}

func NewCloudflareService() *CloudflareService {
	return &CloudflareService{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (s *CloudflareService) GetAllZones() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get environment variables
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")

	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=cloudflare_api_token=%s", apiToken),
		fmt.Sprintf("-var=cloudflare_account_id=%s", accountId),
	}

	// Initialize Terraform only if needed
	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := "cloudflare"
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)
		return nil, tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// Run terraform apply
	fmt.Println("Running terraform apply...")
	applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
	tfApply := exec.Command("terraform", applyArgs...)
	tfApply.Dir = terraformDir
	tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()

	if tfApplyErr != nil {
		fmt.Printf("terraform apply failed - output: %s\n", string(tfApplyOutput))
		fmt.Printf("terraform apply failed - error: %v\n", tfApplyErr)
		return nil, tfApplyErr
	}

	fmt.Println("terraform apply successful")

	// Parse terraform apply output and convert to CloudflareResponse
	return s.parseTerraformApplyOutput(string(tfApplyOutput))
}

// parseTerraformApplyOutput parses terraform apply output and converts to CloudflareResponse
func (s *CloudflareService) parseTerraformApplyOutput(output string) (*dto.CloudflareResponse, error) {
	// First, try to get JSON output using terraform output command
	if response, err := s.getTerraformOutputFromCommand(); err == nil {
		return response, nil
	}

	// If that fails, use DTO parsing methods
	return dto.NewCloudflareResponseFromTerraformOutput(output)
}

// getTerraformOutputFromCommand gets terraform output using terraform output command
func (s *CloudflareService) getTerraformOutputFromCommand() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get terraform output in JSON format
	tfOutput := exec.Command("terraform", "output", "-json")
	tfOutput.Dir = terraformDir
	outputBytes, err := tfOutput.CombinedOutput()

	if err != nil {
		fmt.Printf("terraform output failed - output: %s\n", string(outputBytes))
		fmt.Printf("terraform output failed - error: %v\n", err)
		return nil, err
	}

	return dto.NewCloudflareResponseFromJSON(string(outputBytes))
}

// forceInitializeTerraform forces Terraform re-initialization
func (s *CloudflareService) forceInitializeTerraform(terraformDir string) error {
	fmt.Println("Force re-initializing Terraform...")

	// Remove .terraform directory to force clean init
	terraformStateDir := filepath.Join(terraformDir, ".terraform")
	if err := os.RemoveAll(terraformStateDir); err != nil {
		fmt.Printf("Warning: failed to remove .terraform directory: %v\n", err)
	}

	// Remove lock file
	lockFile := filepath.Join(terraformDir, ".terraform.lock.hcl")
	if err := os.Remove(lockFile); err != nil && !os.IsNotExist(err) {
		fmt.Printf("Warning: failed to remove lock file: %v\n", err)
	}

	// Run terraform init
	tfInit := exec.Command("terraform", "init", "-lock=false")
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()

	if tfInitErr != nil {
		fmt.Printf("terraform force init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform force init failed - error: %v\n", tfInitErr)
		return tfInitErr
	}

	fmt.Println("terraform force init successful")
	return nil
}

// GetZonesWithAPI makes a direct API call to Cloudflare zones endpoint
func (s *CloudflareService) GetZonesWithAPI(page int, name string, perPage int, accountName string) (*dto.CloudflareAPIZonesResponse, error) {
	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Set default values
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 {
		perPage = 20
	}
	if perPage > 50 {
		perPage = 50
	}

	// Build API URL with query parameters
	apiURL := "https://api.cloudflare.com/client/v4/zones"
	params := url.Values{}
	params.Set("page", strconv.Itoa(page))
	params.Set("per_page", strconv.Itoa(perPage))

	if name != "" {
		params.Set("name", name)
	}
	if accountName != "" {
		params.Set("account.name", accountName)
	}

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	fmt.Printf("Making Cloudflare API request to: %s\n", fullURL)

	// Create HTTP request
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	fmt.Printf("Cloudflare API response status: %d\n", resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var apiResponse dto.CloudflareAPIZonesResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		fmt.Printf("Failed to parse JSON response: %v\n", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// Check if API call was successful
	if !apiResponse.Success {
		errorMsg := "API call failed"
		if len(apiResponse.Errors) > 0 {
			errorMsg = fmt.Sprintf("API call failed: %s", apiResponse.Errors[0].Message)
		}
		return nil, fmt.Errorf(errorMsg)
	}

	fmt.Printf("Successfully retrieved %d zones from Cloudflare API\n", len(apiResponse.Result))

	// Fetch always_use_https setting for each zone
	err = s.enrichZonesWithHTTPSSettings(&apiResponse, apiToken)
	if err != nil {
		fmt.Printf("Warning: Failed to enrich zones with HTTPS settings: %v\n", err)
		// Don't fail the entire request, just log the warning
	}

	return &apiResponse, nil
}

// enrichZonesWithHTTPSSettings fetches always_use_https setting for each zone concurrently
func (s *CloudflareService) enrichZonesWithHTTPSSettings(response *dto.CloudflareAPIZonesResponse, apiToken string) error {
	if len(response.Result) == 0 {
		return nil
	}

	// Use a wait group to handle concurrent requests
	var wg sync.WaitGroup
	// Use a channel to limit concurrent requests to avoid overwhelming the API
	semaphore := make(chan struct{}, 5) // Limit to 5 concurrent requests

	for i := range response.Result {
		wg.Add(1)
		go func(zoneIndex int) {
			defer wg.Done()

			// Acquire semaphore
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			zoneID := response.Result[zoneIndex].ID
			httpsValue := s.getAlwaysUseHTTPSSetting(zoneID, apiToken)
			response.Result[zoneIndex].AlwaysUseHTTPS = httpsValue
		}(i)
	}

	wg.Wait()
	return nil
}

// getAlwaysUseHTTPSSetting fetches the always_use_https setting for a specific zone
func (s *CloudflareService) getAlwaysUseHTTPSSetting(zoneID, apiToken string) string {
	// Build API URL for the specific zone setting
	settingURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/settings/always_use_https", zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", settingURL, nil)
	if err != nil {
		fmt.Printf("Failed to create request for zone %s HTTPS setting: %v\n", zoneID, err)
		return ""
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		fmt.Printf("Failed to fetch HTTPS setting for zone %s: %v\n", zoneID, err)
		return ""
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Failed to read HTTPS setting response for zone %s: %v\n", zoneID, err)
		return ""
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("HTTPS setting API request failed for zone %s with status %d: %s\n", zoneID, resp.StatusCode, string(body))
		return ""
	}

	// Parse JSON response
	var settingResponse dto.CloudflareAPISettingResponse
	if err := json.Unmarshal(body, &settingResponse); err != nil {
		fmt.Printf("Failed to parse HTTPS setting JSON response for zone %s: %v\n", zoneID, err)
		return ""
	}

	// Check if API call was successful
	if !settingResponse.Success {
		errorMsg := "Unknown error"
		if len(settingResponse.Errors) > 0 {
			errorMsg = settingResponse.Errors[0].Message
		}
		fmt.Printf("HTTPS setting API call failed for zone %s: %s\n", zoneID, errorMsg)
		return ""
	}

	fmt.Printf("Successfully retrieved HTTPS setting for zone %s: %s\n", zoneID, settingResponse.Result.Value)
	return settingResponse.Result.Value
}
